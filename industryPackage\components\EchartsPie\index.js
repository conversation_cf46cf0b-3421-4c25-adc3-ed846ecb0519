Component({
  properties: {
    // 饼图数据
    pieData: {
      type: Array,
      value: [],
      observer: 'onDataChange'
    },
    // 饼图颜色
    pieColors: {
      type: Array,
      value: ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7']
    },
    // 外部控制的选中索引
    selectedIndex: {
      type: Number,
      value: 0,
      observer: 'onSelectedIndexChange'
    }
  },

  data: {
    currentIndex: 0, // 当前选中的数据索引
    displayTitle: '', // 当前显示的标题
    displayPercentage: '0.00%' // 当前显示的百分比
  },

  lifetimes: {
    attached() {
      // 初始化显示数据
      this.updateDisplayData();
    }
  },

  methods: {
    // 数据变化时重新渲染
    onDataChange(newData) {
      if (newData && newData.length > 0) {
        this.setData({
          currentIndex: this.properties.selectedIndex || 0 // 使用外部传入的索引或默认为0
        });
        this.updateDisplayData();
      }
    },

    // 外部选中索引变化时的处理
    onSelectedIndexChange(newIndex) {
      if (newIndex !== this.data.currentIndex) {
        this.setData({
          currentIndex: newIndex
        });
        this.updateDisplayData();
      }
    },

    // 更新中心显示的数据
    updateDisplayData() {
      const {pieData} = this.properties;
      const {currentIndex} = this.data;

      if (!pieData || pieData.length === 0) return;

      const currentItem = pieData[currentIndex] || pieData[0];

      // 直接使用数据中的percentage字段
      const percentage = currentItem.percentage || '0.00';

      this.setData({
        displayTitle: currentItem.name,
        displayPercentage: `${percentage}`
      });
    },

    // 处理扇形点击事件
    onSliceClick(e) {
      const index = e.currentTarget.dataset.index;
      const {pieData} = this.properties;

      if (!pieData || pieData.length === 0) return;

      this.setData({
        currentIndex: index
      });
      this.updateDisplayData();

      // 触发事件通知外层组件更新列表状态
      this.triggerEvent('pieClick', {
        index: index,
        data: pieData[index]
      });
    },

    // 根据数据值计算圆形大小
    getCircleSize(item) {
      const {pieData} = this.properties;

      if (!pieData || pieData.length === 0) return 80;

      // 计算总值
      const total = pieData.reduce((sum, data) => sum + (data.value || 0), 0);
      if (total === 0) return 80;

      // 根据比例计算大小，最小60rpx，最大120rpx
      const ratio = (item.value || 0) / total;
      const minSize = 60;
      const maxSize = 120;

      return Math.max(
        minSize,
        Math.min(maxSize, minSize + ratio * (maxSize - minSize))
      );
    }
  }
});
