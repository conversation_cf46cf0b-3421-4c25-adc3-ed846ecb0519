var echarts = require('../ec-canvas/echarts');

let myChart = null;

Component({
  properties: {
    // 饼图数据
    pieData: {
      type: Array,
      value: [],
      observer: 'onDataChange'
    },
    // 饼图颜色
    pieColors: {
      type: Array,
      value: ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7']
    },
    // 内圆半径
    innerRadius: {
      type: String,
      value: '65%'
    },
    // 外圆半径
    outerRadius: {
      type: String,
      value: '85%'
    },
    // 外部控制的选中索引
    selectedIndex: {
      type: Number,
      value: 0,
      observer: 'onSelectedIndexChange'
    }
  },

  data: {
    ec: {},
    forceUseOldCanvas: false,
    centerPercentage: '0.00%',
    currentIndex: 0, // 当前选中的数据索引
    displayTitle: '', // 当前显示的标题
    displayPercentage: '0.00%' // 当前显示的百分比
  },

  lifetimes: {
    attached() {
      // 检测开发者工具
      wx.getSystemInfo({
        success: res =>
          res.platform == 'devtools' &&
          this.setData({
            forceUseOldCanvas: true
          })
      });

      // 初始化 ec 对象
      this.setData({
        ec: {
          onInit: this.initChart.bind(this)
        }
      });
    }
  },

  methods: {
    // 数据变化时重新渲染图表
    onDataChange(newData) {
      if (newData && newData.length > 0) {
        this.setData({
          currentIndex: this.properties.selectedIndex || 0 // 使用外部传入的索引或默认为0
        });
        this.updateDisplayData();
        if (myChart) {
          this.updateChart();
        }
      }
    },

    // 外部选中索引变化时的处理
    onSelectedIndexChange(newIndex) {
      if (newIndex !== this.data.currentIndex) {
        this.setData({
          currentIndex: newIndex
        });
        this.updateDisplayData();
        if (myChart) {
          this.updateChart();
        }
      }
    },

    // 更新中心显示的数据
    updateDisplayData() {
      const {pieData} = this.properties;
      const {currentIndex} = this.data;

      if (!pieData || pieData.length === 0) return;

      const currentItem = pieData[currentIndex] || pieData[0];

      // 直接使用数据中的percentage字段
      const percentage = currentItem.percentage || '0.00';

      this.setData({
        displayTitle: currentItem.name,
        displayPercentage: `${percentage}` // `${percentage}%`
      });
    },

    // 处理饼图点击事件
    onPieClick(params) {
      const {pieData} = this.properties;
      if (!pieData || pieData.length === 0) return;

      // 找到点击的数据索引
      const clickedIndex = pieData.findIndex(item => item.name === params.name);

      if (clickedIndex !== -1) {
        this.setData({
          currentIndex: clickedIndex
        });
        this.updateDisplayData();

        // 实现点击时增加宽度的效果
        this.animateClickEffect();

        // 触发事件通知外层组件更新列表状态
        this.triggerEvent('pieClick', {
          index: clickedIndex,
          data: pieData[clickedIndex]
        });
      }
    },

    // 点击动画效果：只增加宽度，不偏移
    animateClickEffect() {
      const {innerRadius, outerRadius} = this.properties;
      const baseInner = parseInt(innerRadius);
      const baseOuter = parseInt(outerRadius);
      const increase = 8; // 增加的半径值

      // 先增大半径
      myChart.setOption({
        series: [
          {
            radius: [`${baseInner}%`, `${baseOuter + increase}%`]
          }
        ]
      });

      // 300ms后恢复原始大小
      setTimeout(() => {
        myChart.setOption({
          series: [
            {
              radius: [innerRadius, outerRadius]
            }
          ]
        });
        // 然后重新渲染图表以更新选中状态
        this.updateChart();
      }, 300);
    },

    // 初始化图表
    initChart(canvas, width, height, dpr) {
      myChart = echarts.init(canvas, null, {
        width: width,
        height: height,
        devicePixelRatio: dpr
      });

      // 添加点击事件监听
      myChart.on('click', params => {
        this.onPieClick(params);
      });

      this.updateChart();
      this.updateDisplayData(); // 初始化显示第一个数据
      return myChart;
    },

    // 更新图表
    updateChart() {
      const {pieData, pieColors, innerRadius, outerRadius} = this.properties;
      const {currentIndex} = this.data;

      if (!pieData || pieData.length === 0) return;

      // 为数据添加选中状态
      const processedData = pieData.map((item, index) => ({
        ...item,
        selected: index === currentIndex // 当前选中的项
      }));

      const option = {
        color: pieColors,
        series: [
          {
            type: 'pie',
            radius: [innerRadius, outerRadius],
            center: ['50%', '50%'],
            data: processedData,
            selectedMode: 'single', // 单选模式
            selectedOffset: 0, // 禁用选中时的偏移距离
            // 设置emphasis时的半径变化
            emphasis: {
              scale: false, // 禁用缩放
              itemStyle: {
                shadowBlur: 0,
                shadowOffsetX: 0,
                shadowColor: 'transparent'
              }
            },
            // 通过设置不同状态的半径来实现效果
            animationType: 'scale',
            animationEasing: 'elasticOut',
            animationDelay: 0,

            label: {
              show: false
            },
            labelLine: {
              show: false
            },
            itemStyle: {}
          }
        ]
      };

      myChart.setOption(option);

      // 设置默认选中状态
      if (currentIndex >= 0 && currentIndex < pieData.length) {
        myChart.dispatchAction({
          type: 'pieSelect',
          seriesIndex: 0,
          dataIndex: currentIndex
        });
      }
    }
  }
});
