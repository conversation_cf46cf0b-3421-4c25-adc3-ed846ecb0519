.pie-container {
  position: relative;
  width: 500rpx;
  height: 400rpx;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  align-items: center;

  .pie-chart {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: center;
    gap: 20rpx;
    padding: 40rpx;
    width: 100%;
    flex: 1;
  }

  .pie-circle {
    border-radius: 50%;
    transition: all 0.3s ease;
    cursor: pointer;

    &.selected {
      transform: scale(1.2);
      box-shadow: 0 0 20rpx rgba(0, 0, 0, 0.3);
    }
  }

  .center-info {
    width: 100%;
    padding: 20rpx;
    text-align: center;
  }

  .center-content {
    display: flex;
    flex-direction: column;
    align-items: center;

    .center-title {
      font-weight: 600;
      font-size: 28rpx;
      color: #74798c;
      font-family:
        PingFang SC,
        PingFang SC;
    }

    .center-percentages {
      font-family:
        PingFang SC,
        PingFang SC;
      font-weight: 600 !important;
      font-size: 40rpx !important;
      color: #20263a !important;
      line-height: 1;
      margin-top: 24rpx;
    }
  }
}
