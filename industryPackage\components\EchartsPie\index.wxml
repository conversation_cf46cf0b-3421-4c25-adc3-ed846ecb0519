<view class="pie-container">
  <!-- 用view实现的圆形色块图 -->
  <view class="pie-chart">
    <!-- 圆形色块，按比例大小排列 -->
    <view
      class="pie-circle {{currentIndex === index ? 'selected' : ''}}"
      wx:for="{{pieData}}"
      wx:key="index"
      style="background-color: {{pieColors[index]}}; width: {{getCircleSize(item)}}rpx; height: {{getCircleSize(item)}}rpx;"
      bindtap="onSliceClick"
      data-index="{{index}}"
    ></view>
  </view>

  <!-- 中心显示区域 -->
  <view class="center-info">
    <view class="center-content">
      <view class="center-title">{{displayTitle}}</view>
      <view class="center-percentages">{{displayPercentage}}</view>
    </view>
  </view>
</view>
